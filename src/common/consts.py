import os
import sys

ROOT_DIR = os.path.dirname(os.path.dirname(
    os.path.dirname(os.path.abspath(__file__))))
LOG_PATH = os.path.join(ROOT_DIR, 'log') or "./log"
sys.path.insert(0, ROOT_DIR)

ENV_PROD = 'live'
ENV = os.getenv('ENV')

INSTANCE_ID = f"{os.getenv('podName')}-{os.getenv('AIP_SERVICE_VERSION')}"

S3_REGION = 'SG'
S3_ENDPOINT_URL = os.getenv('S3_ENDPOINT_URL') or 'https://s3g.data-infra.shopee.io'
S3_BUCKET_NAME = 'sg-antifraud-dwa-shopeefood'
S3_ORDER_DATA_PREFIX = 'dwa_ms_shopeefood_buyer_place_order_address_hi'
S3_ORDER_DATA_PATH = os.path.join(ROOT_DIR, 'data/order')
S3_ORDER_AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
S3_ORDER_AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')

S3_UMS_REGION = 'SG'
S3_UMS_ENDPOINT_URL = os.getenv('S3_UMS_ENDPOINT_URL') or 'https://proxy.uss.s3.sz.shopee.io'
S3_UMS_BUCKET_NAME = 'address'
S3_UMS_DATA_PREFIX = 'clustering_result'
S3_UMS_LOCAL_DATA_PATH = os.path.join(ROOT_DIR, 'data/clustering_result')
S3_UMS_AWS_ACCESS_KEY_ID = os.getenv('S3_UMS_AWS_ACCESS_KEY_ID')
S3_UMS_AWS_SECRET_ACCESS_KEY = os.getenv('S3_UMS_AWS_SECRET_ACCESS_KEY')

PROCESS_REQUESTS_WITHIN_SECONDS = int(
    os.getenv('PROCESS_REQUESTS_WITHIN_SECONDS') or '60')

EMBEDDING_MODEL_NAME = 'BAAI/bge-m3'
EMBEDDING_CHUNK_SIZE = 20000
EMBEDDING_BATCH_SIZE = 1024
EMBEDDING_MAX_LENGTH = 1024

CLUSTERING_DISTANCE_THRESHOLD = 0.30

NUMBER_OF_DAYS_IN_HISTORY = int(os.getenv('NUMBER_OF_DAYS_IN_HISTORY') or '7')
HISTORICAL_DATA_PATH = os.path.join(ROOT_DIR, 'data/order')
HISTORICAL_CLUSTER_DATA_BASE_PATH = os.path.join(ROOT_DIR, 'data')
HISTORICAL_CLUSTER_DATA_FOLDER = os.path.join(
    HISTORICAL_CLUSTER_DATA_BASE_PATH, S3_UMS_DATA_PREFIX)

DISTRIBUTED_LOCK_PREFIX = 'ac:vn:order_lock:'

REDIS_HOST = os.getenv('REDIS_HOST')
REDIS_PORT = os.getenv('REDIS_PORT')
REDIS_USERNAME = os.getenv('REDIS_USERNAME')
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD')
REDIS_TTL_SECONDS = int(os.getenv('REDIS_TTL_SECONDS') or '300')

REQUEST_KAFKA_USERNAME = os.getenv('REQUEST_KAFKA_USERNAME')
REQUEST_KAFKA_PASSWORD = os.getenv('REQUEST_KAFKA_PASSWORD')
REQUEST_KAFKA_TOPIC = os.getenv('REQUEST_KAFKA_TOPIC')
REQUEST_KAFKA_BOOTSTRAP_SERVERS = os.getenv('REQUEST_KAFKA_BOOTSTRAP_SERVERS')
REQUEST_KAFKA_CONSUMER_GROUP = os.getenv('REQUEST_KAFKA_CONSUMER_GROUP')
REQUEST_KAFKA_SECURITY_PROTOCOL = os.getenv('REQUEST_KAFKA_SECURITY_PROTOCOL')
REQUEST_KAFKA_SASL_MECHANISM = os.getenv('REQUEST_KAFKA_SASL_MECHANISM')

LOGGER_KAFKA_BOOTSTRAP_SERVERS = os.getenv("LOGGER_KAFKA_BOOTSTRAP_SERVERS")
LOGGER_KAFKA_USERNAME = os.getenv("LOGGER_KAFKA_USERNAME")
LOGGER_KAFKA_PASSWORD = os.getenv("LOGGER_KAFKA_PASSWORD")
LOGGER_KAFKA_TOPIC = os.getenv("LOGGER_KAFKA_TOPIC")
LOGGER_KAFKA_SASL_MECHANISM = os.getenv("LOGGER_KAFKA_SASL_MECHANISM")

# Exporter related configurations.
CANCEL_API_SCHEME = os.getenv("CANCEL_API_SCHEME") or "https"
CANCEL_API_PATH = os.getenv("CANCEL_API_PATH")
CANCEL_API_TIMEOUT_MS = int(os.getenv("CANCEL_API_TIMEOUT_MS") or '5000')
