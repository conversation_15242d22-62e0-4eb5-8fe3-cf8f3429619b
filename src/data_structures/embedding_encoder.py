from abc import ABC, abstractmethod
from typing import Union, Optional, List, Any, Dict, Literal
from FlagEmbedding import BGEM3FlagModel

import numpy as np

from src.utils.logger import logger


class AddressEmbeddingEncoder(ABC):
    @abstractmethod
    def encode(
            self,
            sentences: Union[List[str], str],
            batch_size: Optional[int] = None,
            max_length: Optional[int] = None,
            return_dense: Optional[bool] = None,
            return_sparse: Optional[bool] = None,
            return_colbert_vecs: Optional[bool] = None,
            **kwargs: Any
    ) -> Dict[
        Literal["dense_vecs", "lexical_weights", "colbert_vecs"],
        Union[np.ndarray, List[Dict[str, float]], List[np.ndarray]]
    ]:
        pass


class BGEM3FlagModelEmbeddingEncoder(AddressEmbeddingEncoder):
    def __init__(self, model_name: str, use_fp16: bool = False):
        self.model = BGEM3FlagModel(model_name, use_fp16)

        # Warm up the model with a dummy input to avoid first inference latency
        logger.info("Warming up the embedding model...")
        self.model.encode(["dummy text for warmup"], max_length=512)
        logger.info("Model warm-up complete")

    def encode(
            self, sentences: Union[List[str], str], batch_size: Optional[int] = None,
            max_length: Optional[int] = None, return_dense: Optional[bool] = None,
            return_sparse: Optional[bool] = None, return_colbert_vecs: Optional[bool] = None, **kwargs: Any
    ) -> Dict[
        Literal["dense_vecs", "lexical_weights", "colbert_vecs"],
        Union[np.ndarray, List[Dict[str, float]], List[np.ndarray]]
    ]:
        return self.model.encode(
            sentences,
            batch_size=batch_size,
            max_length=max_length,
            return_dense=return_dense,
            return_sparse=return_sparse,
            return_colbert_vecs=return_colbert_vecs,
            **kwargs)
